# 优酷联合会员技术设计文档

## 1. 数据库设计

### 1.1 订单表 (youku_orders)

| 字段名 | 类型 | 长度 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| id | BIGINT | - | 是 | 主键ID |
| order_id | VARCHAR | 64 | 是 | 优酷订单号 |
| mobile | VARCHAR | 11 | 是 | 用户手机号 |
| user_type | VARCHAR | 20 | 是 | 用户类型：new_user/inactive_user/old_user |
| product_code | VARCHAR | 50 | 是 | 商品编码 |
| amount | INT | - | 是 | 金额（分） |
| order_status | VARCHAR | 20 | 是 | 订单状态 |
| pay_time | DATETIME | - | 否 | 支付时间 |
| create_time | DATETIME | - | 是 | 创建时间 |
| update_time | DATETIME | - | 是 | 更新时间 |
| callback_count | INT | - | 是 | 回调次数 |
| last_callback_time | DATETIME | - | 否 | 最后回调时间 |

**索引设计**：
- PRIMARY KEY (id)
- UNIQUE KEY uk_order_id (order_id)
- KEY idx_mobile (mobile)
- KEY idx_status_time (order_status, create_time)

### 1.2 权益发放记录表 (benefit_records)

| 字段名 | 类型 | 长度 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| id | BIGINT | - | 是 | 主键ID |
| order_id | VARCHAR | 64 | 是 | 关联订单号 |
| mobile | VARCHAR | 11 | 是 | 用户手机号 |
| benefit_type | VARCHAR | 20 | 是 | 权益类型：phone_credit/meituan_coupon |
| benefit_value | VARCHAR | 100 | 是 | 权益值 |
| status | VARCHAR | 20 | 是 | 发放状态：pending/success/failed |
| claim_status | VARCHAR | 20 | 是 | 领取状态：pending/claimed/expired |
| claim_token | VARCHAR | 128 | 否 | 权益领取凭证 |
| claim_url | VARCHAR | 500 | 否 | 权益领取链接 |
| grant_time | DATETIME | - | 否 | 发放时间 |
| claim_time | DATETIME | - | 否 | 领取时间 |
| expire_time | DATETIME | - | 否 | 过期时间 |
| create_time | DATETIME | - | 是 | 创建时间 |
| update_time | DATETIME | - | 是 | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- UNIQUE KEY uk_claim_token (claim_token)
- KEY idx_order_id (order_id)
- KEY idx_mobile_type (mobile, benefit_type)
- KEY idx_claim_status (claim_status, expire_time)

### 1.3 回调日志表 (callback_logs)

| 字段名 | 类型 | 长度 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| id | BIGINT | - | 是 | 主键ID |
| order_id | VARCHAR | 64 | 是 | 订单号 |
| request_data | TEXT | - | 是 | 请求数据 |
| response_data | TEXT | - | 是 | 响应数据 |
| sign_verify | TINYINT | - | 是 | 签名验证结果：0失败/1成功 |
| process_status | VARCHAR | 20 | 是 | 处理状态 |
| error_message | VARCHAR | 500 | 否 | 错误信息 |
| create_time | DATETIME | - | 是 | 创建时间 |

**索引设计**：
- PRIMARY KEY (id)
- KEY idx_order_id (order_id)
- KEY idx_create_time (create_time)

## 2. 订单状态机

### 2.1 状态定义

| 状态 | 说明 | 可转换状态 |
|------|------|-----------|
| created | 订单已创建 | paid, failed, cancelled |
| paid | 支付成功 | refunded |
| failed | 支付失败 | - |
| refunded | 已退款 | - |
| cancelled | 已取消 | - |

### 2.2 状态转换规则

```
created → paid: 用户支付成功
created → failed: 支付失败或超时
created → cancelled: 用户主动取消或系统取消
paid → refunded: 发生退款
```

## 3. 权益发放逻辑

### 3.1 权益类型映射

| 用户类型 | 首次权益 | 续费权益 |
|---------|----------|----------|
| new_user | 优酷3天卡 + 5元话费券 | 优酷月卡 + 10元美团券 |
| inactive_user | 优酷3天卡 + 5元话费券 | 优酷月卡 + 10元美团券 |
| old_user | 优酷月卡 + 10元美团券 | 优酷月卡 + 10元美团券 |

### 3.2 发放流程

1. **接收回调** → 验证签名 → 更新订单状态
2. **判断订单状态** → 如果是paid状态，触发权益发放
3. **确定权益类型** → 根据用户类型和订单类型确定权益
4. **发放优酷权益** → 由优酷自动处理
5. **生成权益凭证** → 创建权益领取token和链接
6. **返回权益信息** → 将领取链接返回给优酷
7. **记录发放结果** → 更新权益发放记录表
8. **用户领取权益** → 用户通过优酷卡包或公众号链接领取
9. **更新领取状态** → 用户领取后更新claim_status

### 3.3 权益领取流程

1. **用户点击链接** → 从优酷卡包或公众号进入
2. **验证领取凭证** → 检查token有效性和过期时间
3. **检查领取状态** → 防止重复领取
4. **执行权益发放** → 调用话费充值或美团券接口
5. **更新领取状态** → 标记为已领取并记录时间
6. **返回领取结果** → 告知用户领取成功或失败

## 4. 异常处理机制

### 4.1 回调重试机制

- **重试次数**：最多重试3次
- **重试间隔**：1分钟、5分钟、15分钟
- **重试条件**：签名验证通过但处理失败
- **失败处理**：记录失败日志，人工介入

### 4.2 幂等性保证

- **订单维度**：同一订单号的相同状态回调只处理一次
- **权益维度**：同一订单的权益只发放一次
- **实现方式**：数据库唯一约束 + 业务逻辑判断

### 4.3 数据一致性

- **事务控制**：订单状态更新和权益发放在同一事务中
- **补偿机制**：定时任务检查未发放权益的已支付订单
- **监控告警**：异常订单状态变化告警

## 5. 安全防护

### 5.1 签名验证

```python
def verify_sign(params, secret_key):
    """验证签名"""
    sign = params.pop('sign', '')
    sorted_params = sorted(params.items())
    query_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_string = f'{query_string}&key={secret_key}'
    expected_sign = hashlib.md5(sign_string.encode()).hexdigest().upper()
    return sign == expected_sign
```

### 5.2 防重放攻击

- **时间戳验证**：请求时间与服务器时间差不超过5分钟
- **随机数验证**：可选，增加nonce参数防止重放

### 5.3 IP白名单

```python
ALLOWED_IPS = [
    '*************',  # 优酷服务器IP
    '10.0.0.0/8',     # 内网IP段
]

def check_ip_whitelist(request_ip):
    """检查IP白名单"""
    return request_ip in ALLOWED_IPS
```

## 6. 监控指标

### 6.1 业务指标

- **订单成功率**：支付成功订单数 / 总订单数
- **权益发放成功率**：成功发放权益数 / 应发放权益数
- **回调成功率**：成功处理回调数 / 总回调数
- **用户转化率**：各用户类型的购买转化率

### 6.2 技术指标

- **接口响应时间**：各接口的平均响应时间
- **接口成功率**：各接口的成功率
- **系统可用性**：服务可用时间占比
- **错误率**：系统错误数 / 总请求数

### 6.3 告警规则

- 订单成功率低于95%
- 权益发放成功率低于98%
- 接口响应时间超过3秒
- 连续5分钟内错误率超过1%

## 7. 部署架构

### 7.1 服务部署

```
负载均衡器 (Nginx)
    ↓
应用服务器集群 (2台)
    ↓
数据库主从 (MySQL)
    ↓
缓存集群 (Redis)
```

### 7.2 容灾备份

- **数据库备份**：每日全量备份 + 实时binlog备份
- **应用备份**：多机房部署，自动故障切换
- **监控备份**：多套监控系统，确保监控可用性

## 8. 测试用例

### 8.1 接口测试用例

1. **正常流程测试**
   - 新用户购买1.99元套餐
   - 老用户购买29.9元套餐
   - 支付成功回调处理

2. **异常流程测试**
   - 签名验证失败
   - 订单不存在
   - 重复回调处理
   - 网络超时重试

3. **边界条件测试**
   - 时间戳边界值
   - 金额边界值
   - 手机号格式验证

### 8.2 性能测试

- **并发测试**：1000并发用户购买
- **压力测试**：持续高负载运行
- **稳定性测试**：7×24小时稳定性测试
