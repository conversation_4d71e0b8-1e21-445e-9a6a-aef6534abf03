爱奇艺站外双连包相关接⼝

1. 爱奇艺下单接⼝

接⼝说明：

该接⼝⽤于合作⽅向爱奇艺⽅进⾏下单操作，爱奇艺侧拉起⽀付⻚⾯

请求路径：

线上： https://vip.iqiyi.com/html5VIP/activity/quickPayWrap/index.html

请求⽅式：GET

请求参数：

变量名 数据类型 必须存在 说明

orderNo String Y 合作⽅订单号

payMethod String Y ⽀付⽅式，微信：weixin；⽀付宝：alipay

skuId String Y 售卖连包商品的skuId，爱奇艺⽅提供

fv String Y 渠道，爱奇艺侧透传统计使⽤，⾮业务参数

sign String Y 签名，以上所有参数参与签名

Tips：⼀个orderNo只⽀持⼀次下单⾏为（即使未⽀付），再次使⽤相同orderNo请求会返回订单已存

在，⽤⼾重复下单需使⽤不同的orderNo。

代码块

https://vip.iqiyi.com/html5VIP/activity/quickPayWrap/index.html?

fv=xxxxxxxxxxx&orderNo=xxxxxxxxxx&payMethod=alipay&sign=c74926c57b11c5a1b56a9e2

a192d0eb2&skuId=sku_xxxxxxxxxx

2. 合作⽅相关接⼝

2.1 订单号换⼿机号

接⼝说明：

该接⼝⽤于爱奇艺⽅使⽤合作⽅订单号换取⼿机号

1

请求路径：

线上：合作⽅给定

请求⽅式：POST FORM Content-Type:application/x-www-form-urlencoded

请求参数：

变量名 数据类型 必须存在 说明

orderNo String Y 合作⽅订单号

timestamp String Y 实时时间，10位时间戳

sign String Y 签名算法，以上全部参数参与签名，算法⻅下

响应参数

变量名 数据类型 说明

code Integer 1=成功，其他为失败

msg String 结果描述

data Object 返回参数

mobile String ⽤⼾⼿机号

代码块

{

"code": 1,

"msg": "ok",

"data": {

"mobile": "178xxxx3138" //加密是样例展⽰，返回未加密⼿机号

}

}

2.2 合作⽅回调接⼝

接⼝说明：

该接⼝⽤于爱奇艺⽅同步给合作⽅订单的各状态

请求路径：

线上：合作⽅给定

1

2

3

4

5

6

7

请求⽅式：POST FORM Content-Type:application/x-www-form-urlencoded

请求参数：

变量名 数据类型 必须存在 说明

orderNo String N 合作⽅订单号

timestamp String Y 实时时间，10位时间戳

signOrderNo String N 爱奇艺签约订单号

payOrderNo String N 爱奇艺扣款订单号

type Integer Y 回调类型:1=签约，2=续费，3=解约，4=退款

price Number Y 价格

productId String N 产品区分标识（productId=zhifubao0.1）爱奇艺侧统计使

⽤，⾮业务参数

signTime Integer N 签约时间,10位时间戳

unsignTime Integer N 解约时间,10位时间戳

refundTime Integer N 退款时间,10位时间戳

sign String Y 签名算法，以上全部参数参与签名，算法⻅下

Tips: 订购签约的时候传orderNo，signOrderNo；代扣续费的时候传signOrderNo，payOrderNo；

订购退款的时候传orderNo，signOrderNo；代扣退款的时候，只传payOrderNo；解约的时候，传

orderNo，signOrderNo。

不同产品的区分（⽀付宝双连包，微信双连包）由合作⽅通过合作⽅订单号进⾏区分。

响应参数

变量名 数据类型 说明

code String 1=成功，其他为失败

msg String 结果描述

data Object 返回参数，可为空

代码块

{

"code": 1,

"msg": "ok",

"data": {}

}

3. 签名⽅式

键与值之间⽤=连接，键值对之间⽤&连接，参数根据字典序进⾏排序，paramMap剔除sign字段

（sign字段本⾝不参与签名校验）

代码块

private static StringBuffer getEncodeString(Map paramMap, String key) {

ArrayList<String> arr = new ArrayList<>(paramMap.keySet());

Collections.sort(arr, (o1, o2) -> {

if (o1.toString().compareTo(o2.toString())>0) {

return 1;

} else if (o1.toString().compareTo(o2.toString())==0) {

return 0;

} else {

return -1;

}

});

StringBuffer sb = new StringBuffer();

for (String para : arr) {

sb.append(para).append("=").append(paramMap.get(para)).append("&");

}

sb.delete(sb.length()-1,sb.length()).append(key);

return sb;

}

最后拼接上key，获得⽣成的sign

代码块

public static String getMD5Sign(Map paramMap, String key) {

StringBuffer sb = getEncodeString(paramMap, key);

logger.info("md5 before:"+sb.toString());

return DigestUtils.md5Hex(sb.toString());

}

1

2

3

4

5

1

2

3

4

5

6

7

8

9

10

11

12

13

14

15

16

17

18

1

2

3

4

5

