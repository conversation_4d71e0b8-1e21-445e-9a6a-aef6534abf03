# 优酷联合会员流程图

## 订单状态机图

```mermaid
stateDiagram-v2
    [*] --> created: 创建订单
    
    created --> paid: 支付成功
    created --> failed: 支付失败
    created --> cancelled: 订单取消
    
    paid --> refunded: 申请退款
    
    failed --> [*]: 订单结束
    cancelled --> [*]: 订单结束
    refunded --> [*]: 订单结束
    
    note right of created
        订单创建后等待支付
        超时自动取消
    end note
    
    note right of paid
        支付成功后发放权益
        优酷会员 + 零世纪券
    end note
    
    note right of refunded
        退款后回收权益
        通知用户处理结果
    end note
```

## 权益发放流程图

```mermaid
flowchart TD
    A[接收支付成功回调] --> B{验证签名}
    B -->|失败| C[记录错误日志]
    B -->|成功| D[更新订单状态]

    D --> E{判断用户类型}
    E -->|新用户| F[生成5元话费券凭证]
    E -->|1年未活跃| G[生成5元话费券凭证]
    E -->|老用户| H[生成10元美团券凭证]

    F --> I[创建权益领取链接]
    G --> I
    H --> J[创建权益领取链接]

    I --> K[返回话费券领取链接]
    J --> L[返回美团券领取链接]

    K --> M[记录权益发放状态]
    L --> M

    M --> N[流程结束]
    C --> N

    style A fill:#e3f2fd
    style M fill:#c8e6c9
    style C fill:#ffcdd2
    style N fill:#f3e5f5
```

## 权益领取流程图

```mermaid
flowchart TD
    A[用户点击权益链接] --> B[验证领取凭证]
    B -->|无效| C[显示链接无效页面]
    B -->|有效| D{检查权益状态}

    D -->|已领取| E[显示已领取页面]
    D -->|已过期| F[显示已过期页面]
    D -->|可领取| G{权益类型}

    G -->|话费券| H[调用话费充值接口]
    G -->|美团券| I[调用美团券接口]

    H --> J{充值结果}
    I --> K{券发放结果}

    J -->|成功| L[更新为已领取状态]
    J -->|失败| M[显示领取失败页面]
    K -->|成功| L
    K -->|失败| M

    L --> N[显示领取成功页面]
    N --> O[推送成功通知]

    C --> P[流程结束]
    E --> P
    F --> P
    M --> P
    O --> P

    style A fill:#e3f2fd
    style L fill:#c8e6c9
    style M fill:#ffcdd2
    style P fill:#f3e5f5
```

## 用户类型判断流程图

```mermaid
flowchart TD
    A[用户发起购买] --> B[获取用户手机号]
    B --> C[查询优酷用户历史]
    
    C --> D{是否为优酷用户}
    D -->|否| E[标记为新用户]
    D -->|是| F{最后活跃时间}
    
    F -->|1年内有活跃| G[标记为老用户]
    F -->|1年以上未活跃| H[标记为1年未活跃用户]
    
    E --> I[返回1.99元套餐]
    H --> J[返回2.99元套餐]
    G --> K[返回29.9元套餐]
    
    I --> L[生成支付链接]
    J --> L
    K --> L
    
    L --> M[返回给前端]
    
    style E fill:#c8e6c9
    style H fill:#fff3e0
    style G fill:#ffcdd2
```

## 异常处理流程图

```mermaid
flowchart TD
    A[系统异常发生] --> B{异常类型}
    
    B -->|网络超时| C[重试机制]
    B -->|签名验证失败| D[记录安全日志]
    B -->|数据库异常| E[切换从库]
    B -->|第三方接口异常| F[降级处理]
    
    C --> G{重试次数}
    G -->|<3次| H[延迟重试]
    G -->|>=3次| I[人工介入]
    
    D --> J[安全告警]
    E --> K[主库恢复检测]
    F --> L[使用缓存数据]
    
    H --> M[继续处理]
    I --> N[工单系统]
    J --> O[封禁IP]
    K --> P{主库状态}
    L --> Q[降级响应]
    
    P -->|正常| R[切回主库]
    P -->|异常| S[继续使用从库]
    
    M --> T[流程继续]
    N --> U[人工处理]
    O --> V[安全防护]
    R --> T
    S --> W[持续监控]
    Q --> X[部分功能可用]
    
    style I fill:#ff5722
    style J fill:#f44336
    style N fill:#ff9800
    style U fill:#9c27b0
```

## 监控告警流程图

```mermaid
flowchart TD
    A[系统运行] --> B[数据采集]
    B --> C[指标计算]
    C --> D{阈值检查}
    
    D -->|正常| E[继续监控]
    D -->|异常| F[触发告警]
    
    F --> G{告警级别}
    G -->|低级| H[邮件通知]
    G -->|中级| I[短信通知]
    G -->|高级| J[电话通知]
    
    H --> K[记录告警日志]
    I --> K
    J --> K
    
    K --> L[运维人员处理]
    L --> M{问题解决}
    
    M -->|已解决| N[关闭告警]
    M -->|未解决| O[升级告警]
    
    N --> P[恢复监控]
    O --> Q[通知更高级别]
    
    E --> B
    P --> B
    Q --> R[紧急响应]
    
    style F fill:#ff5722
    style J fill:#f44336
    style R fill:#9c27b0
```

## 数据同步流程图

```mermaid
flowchart TD
    A[订单数据变更] --> B[触发同步任务]
    B --> C[读取变更数据]
    C --> D{数据完整性检查}
    
    D -->|通过| E[数据转换]
    D -->|失败| F[记录错误]
    
    E --> G[目标系统写入]
    G --> H{写入结果}
    
    H -->|成功| I[更新同步状态]
    H -->|失败| J[重试机制]
    
    J --> K{重试次数}
    K -->|<5次| L[延迟重试]
    K -->|>=5次| M[人工介入]
    
    I --> N[同步完成]
    L --> G
    M --> O[创建工单]
    F --> P[数据修复]
    
    P --> Q{修复结果}
    Q -->|成功| E
    Q -->|失败| R[数据回滚]
    
    style N fill:#4caf50
    style M fill:#ff9800
    style R fill:#f44336
```
