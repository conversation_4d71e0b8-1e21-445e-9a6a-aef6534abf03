优酷联合会员相关接口

1. 优酷下单接口

接口说明：

该接口用于零世纪方向优酷方进行下单操作，优酷侧拉起支付页面

请求路径：

线上：https://pay.youku.com/html5VIP/activity/quickPayWrap/index.html

请求方式：GET

请求参数：

变量名 数据类型 必须存在 说明

orderNo String Y 零世纪订单号

payMethod String Y 支付方式，微信：weixin；支付宝：alipay

skuId String Y 售卖联合会员商品的skuId，优酷方提供

fv String Y 渠道，优酷侧透传统计使用，非业务参数

mobile String Y 用户手机号

sign String Y 签名，以上所有参数参与签名

Tips：一个orderNo只支持一次下单行为（即使未支付），再次使用相同orderNo请求会返回订单已存在，用户重复下单需使用不同的orderNo。

代码块

https://pay.youku.com/html5VIP/activity/quickPayWrap/index.html?
fv=xxxxxxxxxxx&orderNo=xxxxxxxxxx&payMethod=alipay&sign=c74926c57b11c5a1b56a9e2a192d0eb2&skuId=sku_xxxxxxxxxx&mobile=13800138000

2. 零世纪相关接口

2.1 订单号换手机号

接口说明：

该接口用于优酷方使用零世纪订单号换取手机号

请求路径：

线上：零世纪方给定

请求方式：POST FORM Content-Type:application/x-www-form-urlencoded

请求参数：

变量名 数据类型 必须存在 说明

orderNo String Y 零世纪订单号

timestamp String Y 实时时间，10位时间戳

sign String Y 签名算法，以上全部参数参与签名，算法见下

响应参数

变量名 数据类型 说明

code Integer 1=成功，其他为失败

msg String 结果描述

data Object 返回参数

mobile String 用户手机号

代码块

{
"code": 1,
"msg": "ok",
"data": {
"mobile": "178xxxx3138" //加密是样例展示，返回未加密手机号
}
}

2.2 零世纪回调接口

接口说明：

该接口用于优酷方同步给零世纪订单的各状态

请求路径：

线上：零世纪方给定

请求方式：POST FORM Content-Type:application/x-www-form-urlencoded

请求参数：

变量名 数据类型 必须存在 说明

orderNo String N 零世纪订单号

timestamp String Y 实时时间，10位时间戳

signOrderNo String N 优酷签约订单号

payOrderNo String N 优酷扣款订单号

type Integer Y 回调类型:1=签约，2=续费，3=解约，4=退款

price Number Y 价格

productId String N 产品区分标识（productId=zhifubao0.1）优酷侧统计使用，非业务参数

signTime Integer N 签约时间,10位时间戳

unsignTime Integer N 解约时间,10位时间戳

refundTime Integer N 退款时间,10位时间戳

mobile String Y 用户手机号

userType String Y 用户类型：1=新用户，2=1年未活跃用户，3=老用户

sign String Y 签名算法，以上全部参数参与签名，算法见下

Tips: 订购签约的时候传orderNo，signOrderNo；代扣续费的时候传signOrderNo，payOrderNo；
订购退款的时候传orderNo，signOrderNo；代扣退款的时候，只传payOrderNo；解约的时候，传
orderNo，signOrderNo。

不同产品的区分（支付宝联合会员，微信联合会员）由零世纪通过零世纪订单号进行区分。

响应参数

变量名 数据类型 说明

code String 1=成功，其他为失败

msg String 结果描述

data Object 返回参数，包含权益信息

benefitUrl String 权益领取链接（仅签约成功时返回）

benefitType String 权益类型：phone_credit=话费券，meituan_coupon=美团券

benefitValue String 权益面额

代码块

{
"code": 1,
"msg": "ok",
"data": {
"benefitUrl": "https://api.lingshiji.com/benefit/claim?token=abc123def456",
"benefitType": "phone_credit",
"benefitValue": "5"
}
}

2.3 权益领取状态查询接口

接口说明：

该接口用于优酷方查询用户权益领取状态

请求路径：

线上：零世纪方给定

请求方式：POST FORM Content-Type:application/x-www-form-urlencoded

请求参数：

变量名 数据类型 必须存在 说明

orderNo String Y 零世纪订单号

mobile String Y 用户手机号

timestamp String Y 实时时间，10位时间戳

sign String Y 签名算法，以上全部参数参与签名，算法见下

响应参数

变量名 数据类型 说明

code Integer 1=成功，其他为失败

msg String 结果描述

data Object 返回参数

benefitStatus String 权益状态：pending=待领取，claimed=已领取，expired=已过期

claimTime Integer 领取时间，10位时间戳

benefitType String 权益类型：phone_credit=话费券，meituan_coupon=美团券

benefitValue String 权益面额

代码块

{
"code": 1,
"msg": "ok",
"data": {
"benefitStatus": "claimed",
"claimTime": 1704085800,
"benefitType": "phone_credit",
"benefitValue": "5"
}
}

2.4 权益领取接口

接口说明：

该接口用于用户通过优酷卡包或公众号链接领取权益

请求路径：

线上：零世纪方给定

请求方式：POST FORM Content-Type:application/x-www-form-urlencoded

请求参数：

变量名 数据类型 必须存在 说明

token String Y 权益领取凭证

mobile String Y 用户手机号

timestamp String Y 实时时间，10位时间戳

sign String Y 签名算法，以上全部参数参与签名，算法见下

响应参数

变量名 数据类型 说明

code Integer 1=成功，其他为失败

msg String 结果描述

data Object 返回参数

claimResult String 领取结果：success=成功，failed=失败，already_claimed=已领取

benefitType String 权益类型：phone_credit=话费券，meituan_coupon=美团券

benefitValue String 权益面额

claimTime Integer 领取时间，10位时间戳

代码块

{
"code": 1,
"msg": "ok",
"data": {
"claimResult": "success",
"benefitType": "phone_credit",
"benefitValue": "5",
"claimTime": 1704085800
}
}

3. 签名方式

键与值之间用=连接，键值对之间用&连接，参数根据字典序进行排序，paramMap剔除sign字段
（sign字段本身不参与签名校验）

代码块

private static StringBuffer getEncodeString(Map paramMap, String key) {
ArrayList<String> arr = new ArrayList<>(paramMap.keySet());
Collections.sort(arr, (o1, o2) -> {
if (o1.toString().compareTo(o2.toString())>0) {
return 1;
} else if (o1.toString().compareTo(o2.toString())==0) {
return 0;
} else {
return -1;
}
});
StringBuffer sb = new StringBuffer();
for (String para : arr) {
sb.append(para).append("=").append(paramMap.get(para)).append("&");
}
sb.delete(sb.length()-1,sb.length()).append(key);
return sb;
}

最后拼接上key，获得生成的sign

代码块

public static String getMD5Sign(Map paramMap, String key) {
StringBuffer sb = getEncodeString(paramMap, key);
logger.info("md5 before:"+sb.toString());
return DigestUtils.md5Hex(sb.toString());
}
