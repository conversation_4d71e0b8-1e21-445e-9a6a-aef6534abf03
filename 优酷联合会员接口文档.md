# 优酷联合会员接口文档

## 1. 项目概述

零世纪与优酷合作推出联合会员服务，为用户提供优酷会员权益和零世纪增值服务权益的组合套餐。

### 1.1 商品权益说明

| 用户类型 | 首次购买价格 | 首次权益 | 续费价格 | 续费权益 |
|---------|-------------|----------|----------|----------|
| 纯优酷新用户 | 1.99元 | 优酷会员3天卡 + 零世纪5元话费券 | 29.9元/月 | 优酷月卡 + 10元美团券 |
| 1年未活跃用户 | 2.99元 | 优酷会员3天卡 + 零世纪5元话费券 | 29.9元/月 | 优酷月卡 + 10元美团券 |
| 老客户 | 29.9元 | 优酷月卡 + 10元美团券 | 29.9元/月 | 优酷月卡 + 10元美团券 |

### 1.2 业务分工

- **零世纪**：提供前端展示页面、订单查询接口、回调处理接口
- **优酷**：提供下单接口、收银台、会员权益发放、订单状态回调

## 2. 接口规范

### 2.1 通用规范

- **协议**：HTTPS
- **编码**：UTF-8
- **数据格式**：JSON
- **签名算法**：MD5
- **时间格式**：yyyy-MM-dd HH:mm:ss

### 2.2 通用响应格式

```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01 12:00:00"
}
```

### 2.3 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 签名验证失败 |
| 404 | 订单不存在 |
| 500 | 系统错误 |

## 3. 接口详情

### 3.1 优酷下单接口（优酷提供）

**接口说明**：零世纪调用此接口创建优酷订单并获取支付链接

**请求地址**：`POST https://api.youku.com/partner/order/create`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| partner_id | String | 是 | 合作方ID（零世纪） |
| mobile | String | 是 | 用户手机号 |
| product_code | String | 是 | 商品编码 |
| amount | Integer | 是 | 金额（分） |
| notify_url | String | 是 | 回调地址 |
| return_url | String | 否 | 支付成功跳转地址 |
| timestamp | String | 是 | 时间戳 |
| sign | String | 是 | 签名 |

**响应参数**：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| order_id | String | 优酷订单号 |
| pay_url | String | 支付链接 |
| user_type | String | 用户类型：new_user/inactive_user/old_user |

**请求示例**：

```json
{
    "partner_id": "lingshiji",
    "mobile": "13800138000",
    "product_code": "youku_joint_member",
    "amount": 199,
    "notify_url": "https://api.lingshiji.com/youku/callback",
    "return_url": "https://m.lingshiji.com/success",
    "timestamp": "2024-01-01 12:00:00",
    "sign": "abc123def456"
}
```

**响应示例**：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "order_id": "YK202401011200001",
        "pay_url": "https://pay.youku.com/cashier?order_id=YK202401011200001",
        "user_type": "new_user"
    },
    "timestamp": "2024-01-01 12:00:00"
}
```

### 3.2 订单号换手机号接口（零世纪提供）

**接口说明**：优酷通过订单号查询用户手机号

**请求地址**：`POST https://api.lingshiji.com/youku/order/mobile`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| partner_id | String | 是 | 合作方ID（优酷） |
| order_id | String | 是 | 优酷订单号 |
| timestamp | String | 是 | 时间戳 |
| sign | String | 是 | 签名 |

**响应参数**：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| mobile | String | 用户手机号 |
| order_status | String | 订单状态 |

**请求示例**：

```json
{
    "partner_id": "youku",
    "order_id": "YK202401011200001",
    "timestamp": "2024-01-01 12:00:00",
    "sign": "abc123def456"
}
```

**响应示例**：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "mobile": "13800138000",
        "order_status": "paid"
    },
    "timestamp": "2024-01-01 12:00:00"
}
```

### 3.3 零世纪回调接口（零世纪提供）

**接口说明**：优酷订单状态变化时回调此接口

**请求地址**：`POST https://api.lingshiji.com/youku/callback`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| partner_id | String | 是 | 合作方ID（优酷） |
| order_id | String | 是 | 优酷订单号 |
| mobile | String | 是 | 用户手机号 |
| order_status | String | 是 | 订单状态 |
| amount | Integer | 是 | 金额（分） |
| product_code | String | 是 | 商品编码 |
| user_type | String | 是 | 用户类型 |
| pay_time | String | 否 | 支付时间 |
| timestamp | String | 是 | 时间戳 |
| sign | String | 是 | 签名 |

**订单状态说明**：

| 状态值 | 说明 |
|--------|------|
| created | 订单已创建 |
| paid | 支付成功 |
| failed | 支付失败 |
| refunded | 已退款 |
| cancelled | 已取消 |

**响应参数**：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| benefit_url | String | 权益领取链接（仅支付成功时返回） |
| benefit_type | String | 权益类型：phone_credit/meituan_coupon |
| benefit_value | String | 权益面额 |

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "benefit_url": "https://api.lingshiji.com/benefit/claim?token=abc123",
        "benefit_type": "phone_credit",
        "benefit_value": "5"
    }
}
```

**请求示例**：

```json
{
    "partner_id": "youku",
    "order_id": "YK202401011200001",
    "mobile": "13800138000",
    "order_status": "paid",
    "amount": 199,
    "product_code": "youku_joint_member",
    "user_type": "new_user",
    "pay_time": "2024-01-01 12:05:00",
    "timestamp": "2024-01-01 12:05:00",
    "sign": "abc123def456"
}
```

### 3.4 权益领取状态查询接口（零世纪提供）

**接口说明**：优酷查询用户权益领取状态

**请求地址**：`GET https://api.lingshiji.com/youku/benefit/status`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| partner_id | String | 是 | 合作方ID（优酷） |
| order_id | String | 是 | 优酷订单号 |
| mobile | String | 是 | 用户手机号 |
| timestamp | String | 是 | 时间戳 |
| sign | String | 是 | 签名 |

**响应参数**：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| benefit_status | String | 权益状态：pending/claimed/expired |
| claim_time | String | 领取时间 |
| benefit_type | String | 权益类型 |
| benefit_value | String | 权益面额 |

**请求示例**：

```
GET https://api.lingshiji.com/youku/benefit/status?partner_id=youku&order_id=YK202401011200001&mobile=13800138000&timestamp=2024-01-01 12:00:00&sign=abc123def456
```

**响应示例**：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "benefit_status": "claimed",
        "claim_time": "2024-01-01 12:10:00",
        "benefit_type": "phone_credit",
        "benefit_value": "5"
    }
}
```

### 3.5 权益领取接口（零世纪提供）

**接口说明**：用户通过优酷卡包或公众号链接领取权益

**请求地址**：`POST https://api.lingshiji.com/benefit/claim`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | String | 是 | 权益领取凭证 |
| mobile | String | 是 | 用户手机号 |
| timestamp | String | 是 | 时间戳 |
| sign | String | 是 | 签名 |

**响应参数**：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| claim_result | String | 领取结果：success/failed/already_claimed |
| benefit_type | String | 权益类型 |
| benefit_value | String | 权益面额 |
| claim_time | String | 领取时间 |

**请求示例**：

```json
{
    "token": "abc123def456ghi789",
    "mobile": "13800138000",
    "timestamp": "2024-01-01 12:10:00",
    "sign": "abc123def456"
}
```

**响应示例**：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "claim_result": "success",
        "benefit_type": "phone_credit",
        "benefit_value": "5",
        "claim_time": "2024-01-01 12:10:00"
    }
}
```

## 4. 签名算法

### 4.1 签名生成规则

1. 将所有请求参数（除sign外）按参数名ASCII码升序排列
2. 将排序后的参数按照"key=value"格式拼接，参数间用"&"连接
3. 在拼接字符串末尾加上密钥：`string + "&key=" + secret_key`
4. 对最终字符串进行MD5加密，转为大写

### 4.2 签名示例

**原始参数**：
```
partner_id=lingshiji
mobile=13800138000
amount=199
timestamp=2024-01-01 12:00:00
```

**排序后拼接**：
```
amount=199&mobile=13800138000&partner_id=lingshiji&timestamp=2024-01-01 12:00:00&key=your_secret_key
```

**MD5加密后**：
```
ABC123DEF456GHI789JKL012MNO345PQ
```

### 4.3 签名验证

接收方收到请求后，按照相同规则生成签名，与请求中的sign参数进行比较验证。

## 5. 安全规范

1. **HTTPS传输**：所有接口必须使用HTTPS协议
2. **签名验证**：所有请求必须包含有效签名
3. **时间戳验证**：请求时间戳与服务器时间差不超过5分钟
4. **IP白名单**：生产环境建议配置IP白名单
5. **密钥管理**：定期更换签名密钥，密钥长度不少于32位

## 6. 测试环境

- **优酷测试环境**：https://test-api.youku.com
- **零世纪测试环境**：https://test-api.lingshiji.com
- **测试密钥**：test_secret_key_32_characters_long
