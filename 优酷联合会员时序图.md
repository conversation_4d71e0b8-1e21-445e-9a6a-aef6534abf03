# 优酷联合会员时序图

## 接口调用时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant L as 零世纪前端
    participant LS as 零世纪服务端
    participant Y as 优酷服务端
    participant YP as 优酷收银台
    participant WX as 公众号
    
    U->>L: 访问联合会员页面
    U->>L: 点击购买按钮
    L->>LS: 发起购买请求
    LS->>Y: 调用下单接口
    Note over LS,Y: POST /partner/order/create<br/>携带用户手机号、商品信息
    Y->>Y: 判断用户类型
    Y-->>LS: 返回订单号和支付链接
    LS-->>L: 返回支付链接
    L->>YP: 跳转到优酷收银台
    
    U->>YP: 完成支付操作
    YP->>Y: 支付结果通知
    Y->>Y: 发放优酷会员权益
    
    Y->>LS: 回调订单状态
    Note over Y,LS: POST /youku/callback<br/>订单状态：paid
    LS->>LS: 验证签名
    LS->>LS: 处理订单状态
    LS->>LS: 生成权益领取凭证
    LS-->>Y: 返回权益领取链接

    Y->>Y: 将权益链接展示在卡包
    U->>Y: 在优酷卡包点击权益链接
    Y->>LS: 跳转到权益领取页面
    Note over Y,LS: POST /benefit/claim<br/>携带领取凭证
    LS->>LS: 验证凭证有效性
    LS->>LS: 执行权益发放
    LS-->>Y: 返回领取结果
    Y->>U: 显示领取成功页面
    
    Note over U,WX: 后续续费流程
    Y->>LS: 续费订单回调
    LS->>LS: 发放续费权益
    LS->>WX: 推送续费权益
    
    alt 查询订单场景
        Y->>LS: 查询用户手机号
        Note over Y,LS: POST /youku/order/mobile<br/>通过订单号查询
        LS-->>Y: 返回手机号信息
    end
```

## 支付失败时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant L as 零世纪前端
    participant LS as 零世纪服务端
    participant Y as 优酷服务端
    participant YP as 优酷收银台
    
    U->>L: 访问联合会员页面
    U->>L: 点击购买按钮
    L->>LS: 发起购买请求
    LS->>Y: 调用下单接口
    Y-->>LS: 返回订单号和支付链接
    LS-->>L: 返回支付链接
    L->>YP: 跳转到优酷收银台
    
    U->>YP: 支付操作
    YP->>YP: 支付失败
    YP->>Y: 支付失败通知
    
    Y->>LS: 回调订单状态
    Note over Y,LS: POST /youku/callback<br/>订单状态：failed
    LS->>LS: 验证签名
    LS->>LS: 更新订单状态为失败
    LS-->>Y: 返回处理结果
    
    YP->>U: 显示支付失败页面
    U->>L: 返回零世纪页面
    L->>LS: 查询订单状态
    LS-->>L: 返回失败状态
    L->>U: 显示支付失败，引导重新购买
```

## 退款处理时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant CS as 客服系统
    participant LS as 零世纪服务端
    participant Y as 优酷服务端
    participant WX as 公众号

    U->>CS: 申请退款
    CS->>LS: 查询订单信息
    LS-->>CS: 返回订单详情
    CS->>Y: 发起退款申请
    Y->>Y: 处理退款
    Y->>Y: 回收优酷会员权益

    Y->>LS: 回调退款状态
    Note over Y,LS: POST /youku/callback<br/>订单状态：refunded
    LS->>LS: 验证签名
    LS->>LS: 处理退款状态
    LS->>LS: 标记权益为已过期
    LS-->>Y: 返回处理结果

    LS->>WX: 推送退款通知
    WX->>U: 通知退款成功
    LS->>CS: 更新工单状态
    CS->>U: 退款处理完成通知
```

## 权益领取时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant Y as 优酷卡包
    participant LS as 零世纪服务端
    participant PS as 权益系统
    participant WX as 公众号

    Note over U,Y: 用户在优酷卡包看到权益
    U->>Y: 点击权益领取链接
    Y->>LS: 跳转到权益领取页面
    Note over Y,LS: GET /benefit/claim?token=xxx

    LS->>LS: 验证token有效性
    LS->>LS: 检查权益状态

    alt 权益可领取
        LS->>PS: 调用权益发放接口
        PS-->>LS: 返回发放结果
        LS->>LS: 更新领取状态
        LS->>U: 显示领取成功页面
        LS->>WX: 推送领取成功通知
    else 权益已领取
        LS->>U: 显示已领取页面
    else 权益已过期
        LS->>U: 显示过期页面
    end

    Note over U,WX: 后续查询权益状态
    Y->>LS: 查询权益状态
    Note over Y,LS: GET /youku/benefit/status
    LS-->>Y: 返回领取状态
    Y->>U: 更新卡包显示状态
```
